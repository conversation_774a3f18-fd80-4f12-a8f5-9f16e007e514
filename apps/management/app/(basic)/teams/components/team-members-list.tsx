"use client"

import { User, <PERSON><PERSON>he<PERSON> } from "lucide-react"
import { Badge } from "@ragtop-web/ui/components/badge"
import { DataTable, type ColumnDef } from "@ragtop-web/ui/components/data-table"
import { type Team, type User as UserType } from "../page"
import { useTeamsMember, type TeamMember } from "@/service/team-service"
import { useState } from "react"

interface TeamMembersListProps {
  id: string
}

const getRoles=(roles?:string[])=>{
  if(roles?.includes("TEAM_ADMIN")){
    return "admin"
  }else {
    return "user"
  }
}

export function TeamMembersList({id}:TeamMembersListProps) {

  const team_id = id
 // 分页状态
   const [pageNumber, setPageNumber] = useState(1)
   const [pageSize, setPageSize] = useState(10)
    const { data, isLoading, isFetching } = useTeamsMember(pageNumber, pageSize,team_id)
  console.log(data?.records)
  // 定义表格列
  const columns: ColumnDef<TeamMember>[] = [
    {
      accessorKey: "login_name",
      header: "成员名称",
      cell: ({ row }) => (
        <div className="font-medium flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          {row.original.users.login_name}
        </div>
      ),
    },
    {
      accessorKey: "role",
      header: "角色",
      cell: ({ row }) => {
        const role = getRoles(row.original.roles);
        return role === "admin" ? (
          <Badge className="flex items-center gap-1 w-fit">
            <ShieldCheck className="h-3 w-3" />
            管理员
          </Badge>
        ) : (
          <Badge variant="secondary" className="w-fit">
            成员
          </Badge>
        );
      },
    },
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">团队成员</h3>

      {data?.records && data.records.length > 0 ? (
        <DataTable columns={columns} data={data.records} />
      ) : (
        <div className="rounded-md border p-4 text-center text-muted-foreground">
          暂无团队成员
        </div>
      )}
    </div>
  )
}
